% --- MATLAB Code for iALA Spiral Search Visualization ---

% 清理工作区和命令窗口
clear;
clc;
close all;

% --- 1. 定义核心点的位置 ---
% 为了可视化，我们手动设置这些点的位置
Z_best = [0.5, 0.6];      % 当前最优解 (红星)
Z_current = [-0.8, -0.9]; % 搜索代理当前位置 (绿色三角)
Z_rand = [0.8, -0.5];      % 随机个体位置 (黄色方块)

% --- 2. 模拟螺旋搜索路径 ---
% 这里的目标是生成一个从当前位置Z_current朝向最优位置Z_best的螺旋线
figure('Color', 'white');
hold on;
axis equal; % 保持坐标轴比例一致
grid on;

% 计算从当前点到最优点的向量
vec_to_best = Z_best - Z_current;

% 生成螺旋线
% 我们使用参数方程来创建螺旋，并使其逐渐收敛到Z_best
theta = linspace(5*pi, 0, 200); % 角度从大到小，使其从外向内
radius = linspace(1, 0, 200);   % 半径从大到小，实现收敛
a = vec_to_best(1) / 2; % 螺旋在x轴的幅度
b = vec_to_best(2) / 2; % 螺旋在y轴的幅度

% 螺旋线公式，并平移到以Z_current和Z_best中点为中心
spiral_x = Z_current(1) + (a * radius .* cos(theta)) + vec_to_best(1) * (1-radius);
spiral_y = Z_current(2) + (b * radius .* sin(theta)) + vec_to_best(2) * (1-radius);

% 绘制螺旋路径
plot(spiral_x, spiral_y, 'b--', 'LineWidth', 1.5);

% --- 3. 计算并标记下一次迭代的位置 ---
% Z_next是沿着螺旋线移动一小步后的位置
% 我们从螺旋线数据中选择一个点作为下一次迭代的位置
next_iter_index = 25; % 假设这是下一次迭代的点
Z_next = [spiral_x(next_iter_index), spiral_y(next_iter_index)];

% --- 4. 绘制所有点和标签 ---
% 使用您流程图的配色方案
plot_Z_best = plot(Z_best(1), Z_best(2), '*', 'Color', [0.9, 0.2, 0.2], 'MarkerSize', 14, 'LineWidth', 2);
plot_Z_current = plot(Z_current(1), Z_current(2), '^', 'Color', [0.2, 0.7, 0.3], 'MarkerFaceColor', [0.2, 0.7, 0.3], 'MarkerSize', 10);
plot_Z_rand = plot(Z_rand(1), Z_rand(2), 's', 'Color', [0.9, 0.7, 0.1], 'MarkerFaceColor', [0.9, 0.7, 0.1], 'MarkerSize', 10);
plot_Z_next = plot(Z_next(1), Z_next(2), '^', 'Color', [0.3, 0.75, 0.93], 'MarkerFaceColor', [0.3, 0.75, 0.93], 'MarkerSize', 10);

% --- 5. 添加箭头和文字说明 ---
% 添加从 Z_current 指向 Z_next 的箭头
quiver(Z_current(1), Z_current(2), Z_next(1)-Z_current(1), Z_next(2)-Z_current(2), 0, 'Color', 'k', 'LineWidth', 1, 'MaxHeadSize', 0.5);

% 添加从 Z_current 指向 Z_best 的辅助向量
plot([Z_current(1), Z_best(1)], [Z_current(2), Z_best(2)], 'k:');
% 添加从 Z_rand 指向 Z_best 的辅助向量
plot([Z_rand(1), Z_best(1)], [Z_rand(2), Z_best(2)], 'k:');

% 添加文字标签
text(Z_best(1) + 0.05, Z_best(2), '$\vec{Z}_{best}(t)$', 'Interpreter', 'latex', 'FontSize', 14);
text(Z_current(1) - 0.2, Z_current(2) - 0.1, '$\vec{Z}_{i}(t)$', 'Interpreter', 'latex', 'FontSize', 14);
text(Z_rand(1) + 0.05, Z_rand(2), '$\vec{Z}_{k}(t)$', 'Interpreter', 'latex', 'FontSize', 14);
text(Z_next(1) + 0.05, Z_next(2) + 0.05, '$\vec{Z}_{i}(t+1)$', 'Interpreter', 'latex', 'FontSize', 14);
text(mean(spiral_x), mean(spiral_y) + 0.2, '螺旋搜索', 'FontName', 'SimHei', 'FontSize', 12);


% --- 6. 设置图表样式和图例 ---
% 设置坐标轴
xlabel('$Z_1$', 'Interpreter', 'latex', 'FontSize', 14);
ylabel('$Z_2$', 'Interpreter', 'latex', 'FontSize', 14, 'Rotation', 0);
title('改进ALA算法中的螺旋搜索模型 (iALA Spiral Search Model)', 'FontName', 'SimHei', 'FontSize', 16);
xlim([-1.2 1.2]);
ylim([-1.2 1.2]);
set(gca, 'FontSize', 12);

% 创建图例，并使用流程图的颜色
legend([plot_Z_best, plot_Z_current, plot_Z_next, plot_Z_rand], ...
       '当前最优解', '搜索代理当前位置', '下一次迭代位置', '随机个体位置', ...
       'Location', 'northeast', 'FontName', 'SimHei');

hold off;