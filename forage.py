import matplotlib.pyplot as plt
import numpy as np

# --- 图6：觅食模型 (Foraging Model) ---
fig, ax = plt.subplots(figsize=(8, 6))

# 定义坐标点
z_best = np.array([0.5, 0.7])  # 当前最优解
z_i_t = np.array([0.6, 0.3])   # 当前搜索代理位置

# 绘制坐标轴
ax.arrow(0, 0, 1, 0, head_width=0.03, head_length=0.05, fc='black', ec='black', label='Z2')
ax.arrow(0, 0, 0, 1, head_width=0.03, head_length=0.05, fc='black', ec='black', label='Z1')
ax.text(1.05, 0, '$Z_2$', fontsize=14)
ax.text(0, 1.05, '$Z_1$', fontsize=14)

# 绘制向量
ax.arrow(0, 0, z_best[0], z_best[1], head_width=0.03, head_length=0.05, fc='black', ec='black')
ax.arrow(0, 0, z_i_t[0], z_i_t[1], head_width=0.03, head_length=0.05, fc='black', ec='black')
ax.text(z_best[0] * 0.4, z_best[1] * 1.1, r'$\vec{Z}_{best}(t)$', fontsize=12)
ax.text(z_i_t[0] * 0.5, z_i_t[1] * 0.4, r'$\vec{Z}_i(t)$', fontsize=12)

# 绘制点
ax.plot(z_best[0], z_best[1], '*', color='red', markersize=20, label='当前最优解')
ax.plot(z_i_t[0], z_i_t[1], '^', color='green', markersize=12, label='当前搜索代理位置', alpha=0.7)

# 绘制圆形搜索区域
radius = 0.15
circle = plt.Circle(z_i_t, radius, color='black', fill=False, linestyle='--')
ax.add_patch(circle)
ax.text(z_i_t[0] + radius*0.7, z_i_t[1] - radius*0.7, 'radius', fontsize=12, rotation=-45)

# 绘制下一个迭代位置
z_i_t1_1 = np.array([0.2, 0.6])
z_i_t1_2 = np.array([0.7, 0.8])
ax.plot(z_i_t1_1[0], z_i_t1_1[1], 's', color='cornflowerblue', markersize=12, label='下一个迭代位置', alpha=0.7)
ax.plot(z_i_t1_2[0], z_i_t1_2[1], 's', color='cornflowerblue', markersize=12, alpha=0.7)
ax.text(z_i_t1_1[0], z_i_t1_1[1] - 0.08, r'$\vec{Z}_i(t+1)$', fontsize=12)
ax.text(z_i_t1_2[0], z_i_t1_2[1] + 0.03, r'$\vec{Z}_i(t+1)$', fontsize=12)

# 绘制波浪线
t = np.linspace(0, 1, 100)
wave_x = z_i_t1_1[0] * (1-t) + z_i_t1_2[0] * t
wave_y = z_i_t1_1[1] * (1-t) + z_i_t1_2[1] * t + 0.02 * np.sin(t * 5 * np.pi)
ax.plot(wave_x, wave_y, 'k--')

# 设置图例和标题
ax.legend(handles=[
    plt.Line2D([0], [0], marker='*', color='w', markerfacecolor='red', markersize=15, label='Current optimal solution'),
    plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='green', markersize=10, label='Current position of the search agent', alpha=0.7),
    plt.Line2D([0], [0], marker='s', color='w', markerfacecolor='cornflowerblue', markersize=10, label='Position of the search agent in the next iteration', alpha=0.7)
], bbox_to_anchor=(1.05, 1), loc='upper left', borderaxespad=0.)

ax.set_xlim(0, 1.2)
ax.set_ylim(0, 1.2)
ax.set_aspect('equal', adjustable='box')
ax.set_title('Fig. 6 Schematic illustration of the foraging model in two dimensions', y=-0.15)
plt.gca().axes.get_xaxis().set_visible(False)
plt.gca().axes.get_yaxis().set_visible(False)
plt.box(False)
plt.show()