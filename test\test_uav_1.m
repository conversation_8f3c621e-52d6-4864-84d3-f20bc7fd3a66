clc
clear
close all

Tsim=3000;
delete = 0.1;
Nsim = Tsim/delete;
time = 0: delete: Tsim;

areaSize = [10000, 10000]; % 地图范围
gridSize = 200;
gridDims = areaSize / gridSize;

UAV_state{1} = zeros(Nsim,4); UAV_state{2} = zeros(Nsim,4);
grid_pos_uav{1} = zeros(Nsim,2); grid_pos_uav{2} = zeros(Nsim,2);
grid_des_uav{1} = zeros(2,1); grid_des_uav{2} = zeros(2,1); 
des_v_uav{1} = 0; des_v_uav{2} = 0;
des_heading_uav{1} = 0; des_heading_uav{2} = 0;

USV_state{1} = zeros(Nsim,4); USV_state{2} = zeros(Nsim,4); USV_state{3} = zeros(Nsim,4); USV_state{4} = zeros(Nsim,4);
grid_pos_usv{1} = zeros(Nsim,2); grid_pos_usv{2} = zeros(Nsim,2); grid_pos_usv{3} = zeros(Nsim,2); grid_pos_usv{4} = zeros(Nsim,2); 
grid_des_usv{1} = zeros(2,1); grid_des_usv{2} = zeros(2,1); grid_des_usv{3} = zeros(2,1); grid_des_usv{4} = zeros(2,1);
des_v_usv{1} = 0; des_v_usv{2} = 0; des_v_usv{3} = 0; des_v_usv{4} = 0;
des_heading_usv{1} = 0; des_heading_usv{2} = 0; des_heading_usv{3} = 0; des_heading_usv{4} = 0;

UAV0_1 = [0.001, 4000]; UAV0_2 = [0.001, 6000];
USV0_1 = [0.001, 3500]; USV0_2 = [0.001, 4500]; USV0_3 = [0.001, 5500]; USV0_4 = [0.001, 6500];
UAV_state{1}(1,1:2) = UAV0_1; UAV_state{2}(1,1:2) = UAV0_2;
USV_state{1}(1,1:2) = USV0_1; USV_state{2}(1,1:2) = USV0_2; USV_state{3}(1,1:2) = USV0_3; USV_state{4}(1,1:2) = USV0_4;

for i = 1:8
    tar_index(i) = i+999;
end
tar_index_1 = tar_index;
tar_pos{1} = gridSize * ([25, 30] - 0.5);
tar_pos{2} = gridSize * ([30, 40] - 0.5);
tar_pos{3} = gridSize * ([35, 30] - 0.5);
tar_pos{4} = gridSize * ([28, 25] - 0.5);
tar_pos{5} = gridSize * ([32, 30] - 0.5);
tar_pos{6} = gridSize * ([21, 16] - 0.5);
tar_pos{7} = gridSize * ([26, 16] - 0.5);
tar_pos{8} = gridSize * ([34, 37] - 0.5);
tar_pos_1 = tar_pos;

speed_max_UAV = 33.33; % 无人机最大速度
speed_max_USV = 10.29; % 无人机最大速度
dt_heading_max = pi/4; % 智能体最大转艏角度
Ob_psi = pi/6; % 智能体最大观测范围
Ob_radius_max_UAV = 3000; % 智能体最大观测半径
Ob_radius_max_USV = 800; % 智能体最大观测半径

% 设置随机数种子
rng('default');
% 生成10个二维点，每个坐标在 [0, 10000]
tarPoints.pos = rand(10, 2) * 10000;
tarPoints.pos_grid = Coordinate_rasterization(tarPoints.pos, gridSize);
tarPoints.index = [1:size(tarPoints.pos,1)]';
% Grid_coordinate(tarPoints.pos_grid, gridSize);

tar_find.index = [];
tar_find.num_diss = [];
tar_find.first_findT = [];
tar_find.last_findT = [];
tar_find.duration = [];
tar_find.duration_max = [];
tar_find.final_position = [];
tar_find.find_state = [];
tar_find.tar_find_rate = [];
tar_find.targetMap = [];

tar_find1 = tar_find;

tar_Disposal = [];

tarpre_evo_time = 10; 
decmake_time = 20; predicted_steps = 3;

visit_status = []; % 决策间隔访问位置
Last_visit_time = nan(gridDims); % 最后被访问时间
visit_status_usv = []; % 决策间隔无人艇访问位置
Last_visit_time_usv = nan(gridDims); % 最后被无人艇访问时间

ApheromMap = zeros(gridDims); % 吸引信息素矩阵
RpheromMap = zeros(gridDims); % 排斥信息素矩阵
ApheromMap_usv = zeros(gridDims); % 无人艇吸引信息素矩阵
RpheromMap_usv = zeros(gridDims); % 无人艇排斥信息素矩阵

TargetProMap = 1 / (areaSize(1) * areaSize(2) * gridSize^2) * ones(gridDims); % 目标概率密度矩阵

Initial_rate = 0.1;
Complete_time = 10;
delta_0 = 160;
delta_e = 25; % 目标维纳过程方差
Ea = 0.3; Ga = 0.4; da = 5;
Er = 0.3; Gr = 0.4; dr = 10;
T_aph_switch_UAV = 300; % 吸引信息素释放开关时间 
T_aph_switch_USV = 300;
fitness_plus = zeros(1,6);
Alpha_Aph = 1;
Alpha_Rph = 1;
Alpha_tar = 10;
Alpha_act_con = 100;
Alpha_re = 15;


for loop = 1:Nsim
    ting = (loop - 1) * delete;
%% 访问状态与访问时间更新
    for i_uav = 1:numel(UAV_state)
        grid_pos_uav{i_uav}(loop, :) = Coordinate_rasterization(UAV_state{i_uav}(loop, 1:2), gridSize);
        if isempty(visit_status)
            visit_status = [visit_status; grid_pos_uav{i_uav}(loop, :)];
        elseif ~ismember(grid_pos_uav{i_uav}(loop, :), visit_status, 'rows')
            visit_status = [visit_status; grid_pos_uav{i_uav}(loop, :)];
        end
        Last_visit_time(grid_pos_uav{i_uav}(loop, 1), grid_pos_uav{i_uav}(loop, 2)) = ting; % 最后被访问时间
    end
    for i_usv = 1:numel(USV_state)
        grid_pos_usv{i_usv}(loop, :) = Coordinate_rasterization(USV_state{i_usv}(loop, 1:2), gridSize);

        if isempty(visit_status)
            visit_status = [visit_status; grid_pos_usv{i_usv}(loop, :)];
        elseif ~ismember(grid_pos_usv{i_usv}(loop, :), visit_status, 'rows')
            visit_status = [visit_status; grid_pos_usv{i_usv}(loop, :)];
        end
        Last_visit_time(grid_pos_usv{i_usv}(loop, 1), grid_pos_usv{i_usv}(loop, 2)) = ting; % 最后被访问时间

        if isempty(visit_status_usv)
            visit_status_usv = [visit_status_usv; grid_pos_usv{i_usv}(loop, :)];
        elseif ~ismember(grid_pos_usv{i_usv}(loop, :), visit_status_usv, 'rows')
            visit_status_usv = [visit_status_usv; grid_pos_usv{i_usv}(loop, :)];
        end
        Last_visit_time_usv(grid_pos_usv{i_usv}(loop, 1), grid_pos_usv{i_usv}(loop, 2)) = ting; % 最后被访问时间
    end
%% 决策
    if mod(ting, decmake_time) == 0
        tic
        [ApheromMap, RpheromMap] = updata_pheromMap(ApheromMap, RpheromMap, visit_status, Last_visit_time, ting, T_aph_switch_UAV, gridDims, Ea, Ga, da, Er, Gr, dr);
        [ApheromMap_usv, RpheromMap_usv] = updata_pheromMap(ApheromMap_usv, RpheromMap_usv, visit_status_usv, Last_visit_time_usv, ting, T_aph_switch_USV, gridDims, Ea, Ga, da, Er, Gr, dr);
        pheromMap = Alpha_Aph*ApheromMap - Alpha_Rph *  RpheromMap;
        pheromMap_usv = Alpha_Aph* ApheromMap_usv - Alpha_Rph * RpheromMap_usv;

        Cost_matrix_usv = pheromMap_usv;

        if ~isempty(tar_find.index)
            for ii = 1:size(tar_find.num_diss,2)
                Cost_matrix_usv = Alpha_tar * tar_find.targetMap{ii} + Cost_matrix_usv;
            end
        end

        figure(3); clf;
        [X, Y] = meshgrid(1:gridDims(1), 1:gridDims(2)); % Create meshgrid for x, y coordinates
        mesh(X, Y, pheromMap_usv');
        view(3); hold on;

        figure(4); clf;
        [X, Y] = meshgrid(1:gridDims(1), 1:gridDims(2)); % Create meshgrid for x, y coordinates
        mesh(X, Y, pheromMap');
        view(3); hold on;

        % 无人机决策
        Existing_other_decision_uav = [];
        for i_uav = 1:numel(UAV_state)
            fes_act_dec = get_fes_act_dec(grid_pos_uav{i_uav}(loop, :), speed_max_UAV, decmake_time, predicted_steps, gridDims, gridSize);

            % 启用ALA优化算法
            [optimal_path,hist_best] = ALA_optimization(fes_act_dec, @(x) get_cost(grid_pos_uav{i_uav}(loop, :), UAV_state{i_uav}(loop, 4), x, predicted_steps, Existing_other_decision_uav, 'uav', ...
                ApheromMap, RpheromMap, tar_find, Alpha_Aph, Alpha_Rph, Alpha_tar, Alpha_act_con, Alpha_re, speed_max_UAV, decmake_time, gridDims, gridSize), predicted_steps);
            grid_des_uav{i_uav} = optimal_path(1:2);
            hist_best{1}
            hist_best{2}
            fitness_plus(i_uav) = fitness_plus(i_uav) + hist_best{2}(end);
            Existing_other_decision_uav(end+1,:) = optimal_path;
            [des_v_uav{i_uav}, des_heading_uav{i_uav}] = get_des_vh(UAV_state{i_uav}(loop, 1:2), grid_des_uav{i_uav}, decmake_time, gridSize);
        end

        % 无人艇决策
        Existing_other_decision_usv = [];
        for i_usv = 1:numel(USV_state)
            fes_act_dec = get_fes_act_dec(grid_pos_usv{i_usv}(loop, :), speed_max_USV, decmake_time, predicted_steps, gridDims, gridSize);

            % 启用ALA优化算法
            [optimal_path, hist_best] = ALA_optimization(fes_act_dec, @(x) get_cost(grid_pos_usv{i_usv}(loop, :), USV_state{i_usv}(loop, 4), x, predicted_steps, Existing_other_decision_usv, 'usv', ...
                ApheromMap_usv, RpheromMap_usv, tar_find, Alpha_Aph, Alpha_Rph, Alpha_tar, Alpha_act_con, Alpha_re, speed_max_USV, decmake_time, gridDims, gridSize), predicted_steps);
            grid_des_usv{i_usv} = optimal_path(1:2);
            hist_best{1}
            hist_best{2}
            fitness_plus(numel(UAV_state) + i_usv) = fitness_plus(numel(UAV_state) + i_usv) + hist_best{2}(end);
            Existing_other_decision_usv(end+1,:) = optimal_path;
            [des_v_usv{i_usv}, des_heading_usv{i_usv}] = get_des_vh(USV_state{i_usv}(loop, 1:2), grid_des_usv{i_usv}, decmake_time, gridSize);
       end
        % 决策间隔内访问位置清零
        visit_status = []; % 决策间隔访问位置
        visit_status_usv = []; % 决策间隔无人艇访问位置
        toc
    end

%% 平台位置更新
    % 无人机位置更新
%     tic
    for i_uav = 1:numel(UAV_state)
        UAV_state{i_uav}(loop+1, 3) = des_v_uav{i_uav};
        UAV_state{i_uav}(loop+1, 4) = des_heading_uav{i_uav};
        UAV_state{i_uav}(loop+1, 1:2) = UAV_state{i_uav}(loop, 1:2) + ...
                                       [UAV_state{i_uav}(loop, 3) * cos(UAV_state{i_uav}(loop, 4)), ...
                                        UAV_state{i_uav}(loop, 3) * sin(UAV_state{i_uav}(loop, 4))] * delete;
    end
    % 无人艇位置更新
    for i_usv = 1:numel(USV_state)
        USV_state{i_usv}(loop+1, 3) = des_v_usv{i_usv};
        USV_state{i_usv}(loop+1, 4) = des_heading_usv{i_usv};
        USV_state{i_usv}(loop+1, 1:2) = USV_state{i_usv}(loop, 1:2) + ...
                                       [USV_state{i_usv}(loop, 3) * cos(USV_state{i_usv}(loop, 4)), ...
                                        USV_state{i_usv}(loop, 3) * sin(USV_state{i_usv}(loop, 4))] * delete;
    end
%     toc
    % 判断是否侦察到目标
%     tic
    for i_uav = 1:numel(UAV_state)
        for i_tar = 1:numel(tar_index)
%              dist_to_target = norm(UAV_state{i_uav}(loop, 1:2) - tar_pos{i_tar});
             if Determine_find(UAV_state{i_uav}(loop, 1:2), UAV_state{i_uav}(loop, 4), tar_pos{i_tar}, Ob_radius_max_UAV, pi/6)
%                  display(['时间', num2str(ting),'无人机', num2str(i_uav),'发现目标', num2str(tar_index(i_tar))])
                 % --- 处理目标索引 ---
                 if isempty(tar_find.index) || ~any(tar_find.index == tar_index(i_tar))
                     % 若目标首次被发现，添加到索引列表
                     tar_find.index(end+1) = tar_index(i_tar); % 目标标识号
                     tar_find.num_diss(end+1) = 1; % 同一时刻目标被发现次数
                     tar_find.first_findT(end+1) = ting; % 首次被发现时间
                     tar_find.last_findT(end+1) = ting; % 最后被发现时间
                     tar_find.duration(end+1) = 0;  % 初始化发现持续时长
                     tar_find.duration_max(end+1) = 0;
                     tar_find.final_position{end+1} = tar_pos{i_tar}; % 记录位置
                     tar_find.find_state{end+1} = zeros(Nsim,1);
                     tar_find.find_state{end}(loop,1) = 1;
                     tar_find.tar_find_rate{end+1} = zeros(Nsim,1);
                     tar_find.tar_find_rate{end}(loop,1) = get_Tar_rate(tar_find.duration(end), Initial_rate, Complete_time);
                     tar_find.targetMap{end+1} = find_targetMap(tar_find.final_position{end}, tar_find.tar_find_rate{end}(loop,1), gridDims, gridSize, delta_0);
                 else
                      idx = find(tar_find.index == tar_index(i_tar)); % 提取标识号
                      tar_find.num_diss(idx) = tar_find.num_diss(idx) + 1;
                      if tar_find.num_diss(idx) == 1
                          if tar_find.find_state{idx}(loop-1,1) == 1 % 该目标上一时刻被发现
                              tar_find.last_findT(idx) = ting; % 最后被发现时间更新
                              tar_find.duration(idx) = tar_find.duration(idx) + delete; % 观测持续时间增加
                              if tar_find.duration(idx) > tar_find.duration_max(idx)
                                  tar_find.duration_max(idx) = tar_find.duration(idx);
                              end
                              tar_find.final_position{idx} = tar_pos{i_tar}; % 记录位置
                              tar_find.find_state{idx}(loop,1) = 1;
                              tar_find.tar_find_rate{idx}(loop,1) = get_Tar_rate(tar_find.duration(idx), Initial_rate, Complete_time);
                              tar_find.targetMap{idx} = find_targetMap(tar_find.final_position{idx}, tar_find.tar_find_rate{idx}(loop,1), gridDims, gridSize, delta_0);
                          else % 该目标上一时刻未被发现
                              tar_find.last_findT(idx) = ting; % 最后被发现时间更新
                              tar_find.duration(idx) = 0; % 观测持续时间清零
                              tar_find.final_position{idx} = tar_pos{i_tar}; % 记录位置
                              tar_find.find_state{idx}(loop,1) = 1;
                              tar_find.tar_find_rate{idx}(loop,1) = get_Tar_rate(tar_find.duration(idx), Initial_rate, Complete_time);
                              tar_find.targetMap{idx} = find_targetMap(tar_find.final_position{idx}, tar_find.tar_find_rate{idx}(loop,1), gridDims, gridSize, delta_0);
                          end
                      end
                 end
             end
        end
    end

    for i_usv = 1:numel(USV_state)
        for i_tar = 1:numel(tar_index)
             dist_to_target = norm(USV_state{i_usv}(loop, 1:2) - tar_pos{i_tar});
             if Determine_find(USV_state{i_uav}(loop, 1:2), USV_state{i_uav}(loop, 4), tar_pos{i_tar}, Ob_radius_max_USV, pi)
%                  display(['时间', num2str(ting),'无人艇', num2str(i_usv),'发现目标', num2str(tar_index(i_tar))])
                 % --- 处理目标索引 ---
                 if isempty(tar_find.index) || ~any(tar_find.index == tar_index(i_tar))
                     % 若目标首次被发现，添加到索引列表
                     tar_find.index(end+1) = tar_index(i_tar); % 目标标识号
                     tar_find.num_diss(end+1) = 1; % 同一时刻目标被发现次数
                     tar_find.first_findT(end+1) = ting; % 首次被发现时间
                     tar_find.last_findT(end+1) = ting; % 最后被发现时间
                     tar_find.duration(end+1) = 0;  % 初始化发现持续时长
                     tar_find.duration_max(end+1) = 0;
                     tar_find.final_position{end+1} = tar_pos{i_tar}; % 记录位置
                     tar_find.find_state{end+1} = zeros(Nsim,1);
                     tar_find.find_state{end}(loop,1) = 1;
                     tar_find.tar_find_rate{end+1} = zeros(Nsim,1);
                     tar_find.tar_find_rate{end}(loop,1) = get_Tar_rate(tar_find.duration(end), Initial_rate, Complete_time);
                     tar_find.targetMap{end+1} = find_targetMap(tar_find.final_position{end}, tar_find.tar_find_rate{end}(loop,1), gridDims, gridSize, delta_0);
                 else
                      idx = find(tar_find.index == tar_index(i_tar)); % 提取标识号
                      tar_find.num_diss(idx) = tar_find.num_diss(idx) + 1;
                      if tar_find.num_diss(idx) == 1
                          if tar_find.find_state{idx}(loop-1,1) == 1 % 该目标上一时刻被发现
                              tar_find.last_findT(idx) = ting; % 最后被发现时间更新
                              tar_find.duration(idx) = tar_find.duration(idx) + delete; % 观测持续时间增加
                              if tar_find.duration(idx) > tar_find.duration_max(idx)
                                  tar_find.duration_max(idx) = tar_find.duration(idx);
                              end
                              tar_find.final_position{idx} = tar_pos{i_tar}; % 记录位置
                              tar_find.find_state{idx}(loop,1) = 1;
                              tar_find.tar_find_rate{idx}(loop,1) = get_Tar_rate(tar_find.duration(idx), Initial_rate, Complete_time);
                              tar_find.targetMap{idx} = find_targetMap(tar_find.final_position{idx}, tar_find.tar_find_rate{idx}(loop,1), gridDims, gridSize, delta_0);
                          else % 该目标上一时刻未被发现
                              tar_find.last_findT(idx) = ting; % 最后被发现时间更新
                              tar_find.duration(idx) = 0; % 观测持续时间清零
                              tar_find.final_position{idx} = tar_pos{i_tar}; % 记录位置
                              tar_find.find_state{idx}(loop,1) = 1;
                              tar_find.tar_find_rate{idx}(loop,1) = get_Tar_rate(tar_find.duration(idx), Initial_rate, Complete_time);
                              tar_find.targetMap{idx} = find_targetMap(tar_find.final_position{idx}, tar_find.tar_find_rate{idx}(loop,1), gridDims, gridSize, delta_0);
                          end
                      end
                 end
             end

             % 删除已处置目标
             if dist_to_target <= 100 && ~any(tar_Disposal == tar_index(i_tar))
                 find_index = find(tar_find.index == tar_index(i_tar));
                 display(['时间', num2str(ting),'无人艇', num2str(i_usv),'处置于', num2str(tar_find.first_findT(find_index)), '发现的目标', num2str(tar_index(i_tar))])
                 display(['处置时间', num2str(floor((ting - tar_find.first_findT(find_index)) / 60)),'分钟',num2str(floor((ting - tar_find.first_findT(find_index))-60*floor((ting - tar_find.first_findT(find_index)) / 60))),'秒']);
                 tar_Disposal(end+1) = tar_index(i_tar);
             end
        end
    end
%     toc
    
    if ~isempty(tar_Disposal)
        for i_dis_tar = 1:size(tar_Disposal,2)
            dis_index = tar_Disposal(i_dis_tar);
            tar_find = Delete_target(tar_find, dis_index);
            index_ = find(tar_index == dis_index);
            tar_index(index_) = [];
            tar_pos(index_) = [];
        end
    end

    if ~isempty(tar_find.index)
        for ii = 1:size(tar_find.num_diss,2)
            tar_find.num_diss(ii) = 0; % 同一时间被发现次数清零
            if tar_find.find_state{ii}(loop,1) == 0 && mod(round(ting-tar_find.last_findT(ii), 2), tarpre_evo_time) == 0
                % 若目标此时未被发现，且距离上次被发现的时长经过演化时长的整数倍，则进行目标概率图演化更新
%                 display([num2str(ting),'时', '目标', num2str(ii), '演化更新']);
                tar_find.targetMap{ii} = preTargetMap(tar_find.targetMap{ii}, gridDims, gridSize, delta_e, tarpre_evo_time);
            end

%             if ii ==1
%                 if tar_find.tar_find_rate{ii}(loop,1) ~= 1 && tar_find.tar_find_rate{ii}(loop,1) ~= 0
%                         figure(21)
%                         clf;
%                         [X, Y] = meshgrid(1:gridDims(1), 1:gridDims(2)); % Create meshgrid for x, y coordinates
%                         mesh(X, Y, tar_find.targetMap{ii}');
%                         xlabel('x'); ylabel('y'); zlabel('Probability');  % Add a z-axis label for probability
%                         view(3); % Enable 3D view
%                         hold on
%                 end
%             end
% 
%             if mod(ting, tarpre_evo_time) == 0
%                 figure(ii)
%                 clf;
%                 [X, Y] = meshgrid(1:gridDims(1), 1:gridDims(2)); % Create meshgrid for x, y coordinates
%                 mesh(X, Y, tar_find.targetMap{ii}');
%                 xlabel('x'); ylabel('y'); zlabel('Probability');  % Add a z-axis label for probability
%                 view(3); % Enable 3D view
%                 hold on
%             end
        end
    end

end

figure(11)
for i_uav = 1:numel(UAV_state)
    plot(UAV_state{i_uav}(:, 1), UAV_state{i_uav}(:, 2),'r'); hold on;
end
for i_usv = 1:numel(USV_state)
    plot(USV_state{i_usv}(:, 1), USV_state{i_usv}(:, 2),'b'); hold on;
end
for i_tar = 1:numel(tar_index_1)
    plot(tar_pos_1{i_tar}(1), tar_pos_1{i_tar}(2), 'rp'); hold on
end
xlim([0, areaSize(1)]); ylim([0, areaSize(2)])


figure(12)
subplot(211)
plot(time, USV_state{i_usv}(:, 3))
subplot(212)
plot(time, USV_state{i_usv}(:, 4))
% 
% figure(13)
% subplot(411)
% plot(time(1:end-1), tar_find.find_state{1}(:)); hold on
% subplot(412)
% plot(time(1:end-1), tar_find.find_state{2}(:)); hold on
% subplot(413)
% plot(time(1:end-1), tar_find.find_state{3}(:)); hold on
% subplot(414)
% plot(time(1:end-1), tar_find.find_state{4}(:)); hold on
% % 
% figure(14)
% subplot(411)
% plot(time(1:end-1), tar_find.tar_find_rate{1}(:)); hold on
% subplot(412)
% plot(time(1:end-1), tar_find.tar_find_rate{2}(:)); hold on
% subplot(413)
% plot(time(1:end-1), tar_find.tar_find_rate{3}(:)); hold on
% subplot(414)
% plot(time(1:end-1), tar_find.tar_find_rate{4}(:)); hold on

function Grid_position = Coordinate_rasterization(Consecutive_positions, gridSize)
    n = size(Consecutive_positions, 1);
    m = size(Consecutive_positions, 2);
    Grid_position = zeros(n, m);
    for i = 1:n
        Grid_position(i,:) = max(ceil(Consecutive_positions(i,:)/gridSize), [1,1]);
    end
end

function Consecutive_positions = Grid_coordinate(Grid_position, gridSize)
    n = size(Grid_position, 1);
    m = size(Grid_position, 2);
    Consecutive_positions = zeros(n, m);
    for i = 1:n
        Consecutive_positions(i,:) = (Grid_position(i,:) - 1/2) * gridSize;
    end
end

function [des_v, des_heading] = get_des_vh(position_cur, grid_des, decmake_time, gridSize)
    position_des = Grid_coordinate(grid_des, gridSize);
    deltet = position_des - position_cur;
    dis = norm(deltet);
    des_v = dis / decmake_time;
    des_heading = atan2(deltet(2), deltet(1));
end

function [AphMap, RphMap] = updata_pheromMap(ApheromMap, RpheromMap, visit_status, Last_visit_time, cur_ting, T_aph_switch, gridDims, Ea, Ga, da, Er, Gr, dr)
    % 更新数字信息素图(DPM) - 包括吸引和排斥信息素
    AphMap = zeros(gridDims);
    RphMap = zeros(gridDims);
    CK = zeros(gridDims); % 吸引度释放开关
    T_last = zeros(gridDims);
    for i_visit_p = 1:size(visit_status, 1)
        T_last(visit_status(i_visit_p, 1), visit_status(i_visit_p, 2)) = 1;
    end

    for i = 1:gridDims(1)
        for j = 1:gridDims(2)
            % 计算吸引信息素开关系数
            if isnan( Last_visit_time(i, j) )
                CK(i, j) = 1;
            else
                if cur_ting - Last_visit_time(i, j) >= T_aph_switch
                    CK(i, j) = 1;
                else
                    CK(i, j) = 0;
                end
            end
            % 信息素传播
            neighbors = getNeighbors([i, j], gridDims);
            GP_a = 0;
            GP_r = 0;
            for n = 1:size(neighbors, 1)
                ni = neighbors(n, 1);
                nj = neighbors(n, 2);
                GP_a = GP_a + Ga * (ApheromMap(ni, nj) + da) / size(neighbors, 1);
                GP_r = GP_r + Gr * (RpheromMap(ni, nj) + dr) / size(neighbors, 1);
            end
            % 更新吸引信息素
            if T_last(i, j) == 0
                AphMap(i, j) =  (1-Ea) * ( (1-Ga) * (ApheromMap(i,j) + CK(i,j) * da ) + GP_a);
            end
            % 更新排斥信息素
            RphMap(i, j) = (1-Er) * ( (1-Gr) * (RpheromMap(i,j) + T_last(i,j) * da ) + GP_r);
        end
    end
end

function neighbors = getNeighbors(position_grid, gridDims)
    % 获取相邻栅格，网格范围gridSize
    neighbors = [];
    for di = -1:1
        for dj = -1:1
            if di == 0 && dj == 0
                continue;
            end
            ni = position_grid(1) + di;
            nj = position_grid(2) + dj;
            if ni >= 1 && ni <= gridDims(1) && nj >= 1 && nj <= gridDims(2)
                neighbors = [neighbors; ni, nj];
            end
        end
    end
end

function tar_find_rate = get_Tar_rate(duration, Initial_rate, Complete_time)
    if duration <= Complete_time
        tar_find_rate = Initial_rate + (1 - Initial_rate) * duration / Complete_time;
    else
        tar_find_rate = 1;
    end
end

function targetMap = find_targetMap(final_position, tar_find_rate, gridDims, gridSize, delta_0)
    targetMap = zeros(gridDims);
    Grid_position_ = Coordinate_rasterization(final_position, gridSize);

%     if tar_find_rate == 1
%         targetMap(Grid_position_(1), Grid_position_(2)) = tar_find_rate;
%     else
        for x = 1:gridDims(1)
            for y = 1:gridDims(2)
                targetMap(x,y) = (1 / (2*pi*delta_0^2/tar_find_rate^2)) * exp(-((gridSize*(Grid_position_(1)-x))^2 + (gridSize*(Grid_position_(2)-y))^2) / (2*delta_0^2/tar_find_rate^2));
            end
        end
        targetMap = tar_find_rate * targetMap ./ max(targetMap(:));
%     end
end

function preTargetMap = preTargetMap(targetMap, gridDims, gridSize, delta_e, Evo_time)
    preTargetMap = zeros(gridDims);
    for ix = 1:gridDims(1)
        for iy = 1:gridDims(2)
            f_integral = zeros(gridDims); % 待积分矩阵f_k_k1*f_k1
            for fx = 1:gridDims(1)
                for fy = 1:gridDims(2)
                    f_k_k1 = (1/(2*pi*delta_e^2*Evo_time)) * exp( -((gridSize*(ix-fx))^2 + (gridSize*(iy-fy))^2) / (2*delta_e^2*Evo_time) );
                    f_integral(fx, fy) = f_k_k1 * targetMap(fx, fy);
                end
            end
            preTargetMap(ix, iy) = double_integral(f_integral, gridDims(1), gridDims(2), gridSize); % 更新概率密度矩阵
        end
    end
    preTargetMap  = preTargetMap / sum(preTargetMap(:));
end

function result = double_integral(f, x, y, gridSize)
    [rows, cols] = size(f);
    if x < 1 || x > rows || y < 1 || y > cols
        error('x和y必须在栅格范围内');
    end
    result = 0; % 初始化积分结果
    for i = 1:x     % 计算二维积分(累加求和)
        for j = 1:y
            result = result + f(i,j) * gridSize^2;
        end
    end
end

function tar_find = Delete_target(tar_find, index2deleted)
    idx = find(tar_find.index == index2deleted); % 提取标识号
    tar_find.index(idx) = []; % 清空目标标识号
    tar_find.num_diss(idx) = []; % 清空同一时刻目标被发现次数
    tar_find.first_findT(idx) = []; % 清空首次被发现时间
    tar_find.last_findT(idx) = []; % 清空最后被发现时间
    tar_find.duration(idx) = [];  % 清空初始化发现持续时长
    tar_find.duration_max(idx) = [];
    tar_find.final_position(idx) = []; % 清空记录位置
    tar_find.find_state(idx) = [];
    tar_find.tar_find_rate(idx) = [];
    tar_find.targetMap(idx) = [];
end

function cost = get_cost(pos_cur, heading_cur, Rolling_decision, Rolling_step_size, Existing_other_decision, agent_kind, ApheromMap, RpheromMap, tar_find,...
                         Alpha_Aph, Alpha_Rph, Alpha_tar, Alpha_act_con, Alpha_re, speed_max, decision_time, gridDims, gridSize)
    % 提取各步决策结果
    decision = zeros(Rolling_step_size, 2);
    for step_i = 1:Rolling_step_size
        decision(step_i, :) = Rolling_decision( (2*(step_i-1)+1) : (2*(step_i - 1)+2) );
    end

    % 获取可行动作集
%     grid_pos_cur = Coordinate_rasterization(pos_cur, gridSize);
    grid_pos_cur = pos_cur;
    for step_i = 1:Rolling_step_size
        if step_i == 1
            fes_act{step_i} = get_fes_act(agent_kind, grid_pos_cur, heading_cur, speed_max, decision_time, gridDims, gridSize); % 当前位置可行动作
        elseif step_i == 2
            heading_dec = atan2(decision(step_i-1,2)-grid_pos_cur(2), decision(step_i-1, 1)-grid_pos_cur(1)); % 按第一步动作后艏向
            fes_act{step_i} = get_fes_act(agent_kind, decision(step_i-1, :), heading_dec, speed_max, decision_time, gridDims, gridSize); % 第一步动作后可行动作
        else
            heading_dec = atan2(decision(step_i-1,2)-decision(step_i-2,2), decision(step_i-1, 1)-decision(step_i-2, 1)); % 按第i步动作后艏向
            fes_act{step_i} = get_fes_act(agent_kind, decision(step_i-1, :), heading_dec, speed_max, decision_time, gridDims, gridSize); % 第i步动作后可行动作
        end
    end

    % 邻居决策动作
    if isempty(Existing_other_decision)
        decision_others{1} = [];
    else
        for other_i = 1:size(Existing_other_decision, 1)
            decision_others{other_i} =  zeros(Rolling_step_size, 2);
            for step_i = 1:Rolling_step_size
                decision_others{other_i}(step_i, :) = Existing_other_decision(other_i, (2*(step_i-1)+1) : (2*(step_i - 1)+2) );
            end
        end
    end

    % 信息素+目标存在概率栅格收益
    Cost_grid = Alpha_Aph*ApheromMap - Alpha_Rph*RpheromMap;
    if ~isempty(tar_find.index)
        for ii = 1:size(tar_find.num_diss,2)
            Cost_grid = Alpha_tar * tar_find.targetMap{ii} + Cost_grid;
        end
    end

    J_grid = 0;
    for step_i = 1:Rolling_step_size
        J_grid = J_grid + exp((1-step_i) / Rolling_step_size) * Cost_grid( decision(step_i, 1),  decision(step_i, 2));
    end

    % 可行动作约束惩罚
    J_act_con = 0;
    act_con = zeros(Rolling_step_size, 1);
    for step_i = 1:Rolling_step_size
        if isempty(fes_act{step_i})
            act_con(step_i) = 1;
            J_act_con = J_act_con + exp((1-step_i) / Rolling_step_size) * Alpha_act_con;
        else
            if ~ismember(decision(step_i, :), fes_act{step_i}, 'row')
                act_con(step_i) = 1;
                J_act_con = J_act_con + exp((1-step_i) / Rolling_step_size) * Alpha_act_con;
            end
        end
    end

    % 调节惩罚
    J_re = 0;
    if ~isempty(decision_others{1})
        for other_i = 1:numel(decision_others)
            for step_i = 1:Rolling_step_size
                act_map{step_i} = zeros(gridDims);
                act_map{step_i}(decision(step_i, 1), decision(step_i, 2)) = 1; % 创建决策矩阵，第step_i步决策位置元素处为1
                act_map_other{step_i} = zeros(gridDims);
                act_map_other{step_i}(decision_others{other_i}(step_i, 1), decision_others{other_i}(step_i, 2)) = 1; % 创建决策矩阵，第step_i步决策位置元素处为1
                map_ = act_map{step_i} .* act_map_other{step_i};
                J_re = J_re +  exp((1-step_i) / Rolling_step_size) * sum(map_(:)) * Alpha_re;
            end
        end
    end

    cost = J_grid - J_act_con - J_re;
end

function fes_act = get_fes_act(agent_kind, position_grid, heading, speed_max, decision_time, gridDims, gridSize)
    l_max = speed_max*decision_time;
    fes_act = [];
    if agent_kind == 'uav'
        for di = -floor(l_max/gridSize):floor(l_max/gridSize)
            for dj = -floor(l_max/gridSize):floor(l_max/gridSize)
                if di == 0 && dj == 0
                    continue;
                end
                ni = position_grid(1) + di;
                nj = position_grid(2) + dj;
                dist = norm(gridSize*[di,dj]);
                psi = atan2(dj, di);
                if ni >= 1 && ni <= gridDims(1) && nj >= 1 && nj <= gridDims(2) && dist<=l_max && com_agent(heading, pi/4, psi)
                    fes_act = [fes_act; ni, nj];
                end
            end
        end
    elseif agent_kind == 'usv'
        for di = -floor(l_max/gridSize):floor(l_max/gridSize)
            for dj = -floor(l_max/gridSize):floor(l_max/gridSize)
                if di == 0 && dj == 0
                    continue;
                end
                ni = position_grid(1) + di;
                nj = position_grid(2) + dj;
                if ni >= 1 && ni <= gridDims(1) && nj >= 1 && nj <= gridDims(2)
                    fes_act = [fes_act; ni, nj];
                end
            end
        end
    else
        error('节点类型错误，可行动作获取失败')
    end
end

function fes_act_dec = get_fes_act_dec(position_grid, speed_max, decision_time, predicted_steps, gridDims, gridSize)
    l_max = speed_max*decision_time;
    fes_act_dec = [];
    for di = -predicted_steps * floor(l_max/gridSize) : predicted_steps * floor(l_max/gridSize)
        for dj = -predicted_steps * floor(l_max/gridSize) : predicted_steps * floor(l_max/gridSize)
            if di == 0 && dj == 0
                continue;
            end
            ni = position_grid(1) + di;
            nj = position_grid(2) + dj;
            if ni >= 1 && ni <= gridDims(1) && nj >= 1 && nj <= gridDims(2)
                fes_act_dec = [fes_act_dec; ni, nj];
            end
        end
    end
end


function be_find = Determine_find(agent_pos, agent_heading, tar_pos, Ob_radius, Observation_angle)
    dis = tar_pos - agent_pos ;
    dist2tar = norm(dis);
    agent_com = atan2(dis(2), dis(1));
    be_find = (dist2tar <= Ob_radius) && com_agent(agent_heading, Observation_angle, agent_com);
end

function com_re = com_agent(heading, ob_psi, agent_com)
    heading_norm = mod(heading + pi, 2*pi) - pi;
    agent_com_norm = mod(agent_com + pi, 2*pi) - pi;
    lower_bound = mod(heading_norm - ob_psi + pi, 2*pi) - pi;
    upper_bound = mod(heading_norm + ob_psi + pi, 2*pi) - pi;

    if lower_bound < upper_bound
        % 正常情况：区间没有跨越 -pi/pi 边界
        com_re = (agent_com_norm >= lower_bound) && (agent_com_norm <= upper_bound);

    elseif lower_bound == upper_bound % 上下界相等
        if lower_bound == heading_norm 
            com_re = (agent_com_norm == heading_norm);
        else
            com_re =true;
        end
    else
        % 跨越边界情况：区间跨越了 -pi/pi 边界
        com_re = (agent_com_norm >= lower_bound) || (agent_com_norm <= upper_bound);
    end
end

% ALA算法优化函数
function [best_position, hist_best] = ALA_optimization(fes_act_dec, fitness_func, predicted_steps)
    
    % 算法参数设置
    N = min(25, size(fes_act_dec, 1)); % 种群的大小
    Max_iter = 25; % 最大迭代次数
    vec_flag = [1, -1]; % 方向标志向量
    hist_best{1} = zeros(Max_iter, 2*predicted_steps);
    hist_best{2} = zeros(Max_iter, 1);
   
    % 初始化种群
    X = zeros(N, 2*predicted_steps);
    fitness = zeros(1, N);
    for i = 1:N
        decision_vector = [];
        for step = 1:predicted_steps
            idx = randi(size(fes_act_dec, 1));
            decision_vector = [decision_vector, fes_act_dec(idx, :)];
        end
        X(i, :) = decision_vector;
        fitness(1, i) = fitness_func(X(i, :));
    end
    
    % 找到初始最优解
    Position = zeros(1, 2*predicted_steps);
    Score = -inf;
    for i = 1:N
        if fitness(1, i) > Score
            Position = X(i, :);
            Score = fitness(1, i);
        end
    end
    
    Iter = 1;
    
    % 算法的主循环
    while Iter <= Max_iter
        RB = randn(N, 2*predicted_steps); % 布朗运动
        F = vec_flag(floor(2*rand()+1)); % 随机方向标志
        theta = 2*atan(1-Iter/Max_iter); % 时变参数
        p_cauchy = 1 - (Iter / Max_iter); % 柯西变异概率
        p_guassian = Iter / Max_iter; % 高斯变异概率
        % 位置更新阶段
        Xnew = zeros(N, 2*predicted_steps);
        for i = 1:N
            E = 2*log(1/rand)*theta;
            
            if E > 1 % 高能量状态
                r = rand;
                % 柯西变异
                if r < p_cauchy && r > 0.3
                    cauchy_step = 1.0 - (Iter / Max_iter);
                    cauchy_mutate = cauchy_step.* tan(pi * (rand(1, 2*predicted_steps) - 0.5));
                    Xnew(i, :) = Position + cauchy_mutate;
                elseif r <= 0.3
                    % 群体协作
                    r1 = 2 * rand(1, 2*predicted_steps) - 1;
                    Xnew(i,:) = Position + F.*RB(i,:).*(r1.*(Position-X(i,:))+(1-r1).*(X(i,:)-X(randi(N),:)));
                else 
                    % 定向搜索
                    r2 = rand() * (1 + sin(0.5 * Iter));
                    Xnew(i,:) = X(i,:) + F.* r2*(Position-X(randi(N),:));
                end
            else % 即E<=1，为低能量状态
                r = rand;
                % 高斯变异
                if r < p_guassian && r > 0.5
                    guassian_step = 1 - (Iter / Max_iter);
                    guassian_mutate = guassian_step.*randn(1,2*predicted_steps);
                    Xnew(i, :) = Position + guassian_mutate;
                elseif r <= 0.5
                    % 螺旋搜索
                    radius = sqrt(sum((Position-X(i, :)).^2));
                    r3 = rand();
                    spiral = radius*(sin(2*pi*r3)+cos(2*pi*r3));
                    Xnew(i,:) = Position + F.* X(i,:).*spiral*rand;
                else
                    % Levy飞行
                    G = 2*(sign(rand-0.5))*(1-Iter/Max_iter);
                    beta = 1.5;
                    sigma = (gamma(1+beta)*sin(pi*beta/2)/(gamma((1+beta)/2)*beta*2^((beta-1)/2)))^(1/beta);
                    u = randn(1, 2*predicted_steps)*sigma;
                    v = randn(1, 2*predicted_steps);
                    step = u./abs(v).^(1/beta);
                    Xnew(i,:) = Position + F.* G*step.* (Position - X(i,:));
                end
            end
        end
        
        for i = 1:N
            % 连续解的离散化
            Xnew_discrete = zeros(1, 2*predicted_steps);
            for step = 1:predicted_steps
                step_decision_cont = Xnew(i, (2*(step-1)+1) : (2*step) );
                distances = sum((fes_act_dec - step_decision_cont).^2, 2);
                [~, min_idx] = min(distances);
                Xnew_discrete( (2*(step-1)+1) : (2*step) ) = fes_act_dec(min_idx, :);
            end
            
            % 评估新解
            newPopfit = fitness_func(Xnew_discrete);
            
            % 贪心选择
            if newPopfit > fitness(1, i)
                X(i, :) = Xnew_discrete;
                fitness(1, i) = newPopfit;
            end
            
            % 更新全局最优
            if fitness(1, i) > Score
                Position = X(i, :);
                Score = fitness(1, i);
            end
        end

        hist_best{1}(Iter, :) = Position;
        hist_best{2}(Iter, :) = Score;
        Iter = Iter + 1;
    end
    best_position = Position;
end
