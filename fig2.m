% --- MATLAB Code for iALA Mutation Visualization (FINAL CORRECTED VERSION) ---

% 清理工作区和命令窗口
clear;
clc;
close all;

% --- 1. 初始化参数 ---
num_points = 2000; % 生成的随机样本点数量
Z_best = [0, 0];   % 为方便可视化，将当前最优解置于原点

% --- 2. 创建一个图形窗口 ---
fig = figure('Name', 'iALA 变异策略对比', 'Color', 'white', 'Position', [100, 100, 1200, 600]);

% --- 3. 子图一：高斯变异 (局部精细搜索) ---
subplot(1, 2, 1);
hold on;
grid on;
axis equal;

% 生成高斯分布的随机点 (N(0,1))
gaussian_points = randn(num_points, 2);

% 绘制由高斯变异产生的潜在新位置（点云）
plot(gaussian_points(:,1), gaussian_points(:,2), '.', 'Color', [0.3, 0.75, 0.93], 'MarkerSize', 8);

% 绘制当前最优解 Z_best
plot_Z_best = plot(Z_best(1), Z_best(2), '*', 'Color', [0.9, 0.2, 0.2], 'MarkerSize', 16, 'LineWidth', 2);

% 选择一个点作为变异后的新位置 Z_new
Z_new_gaussian = gaussian_points(1,:);
plot_Z_new_gauss = plot(Z_new_gaussian(1), Z_new_gaussian(2), '^', 'Color', [0.2, 0.7, 0.3], 'MarkerFaceColor', [0.2, 0.7, 0.3], 'MarkerSize', 10, 'LineWidth', 1.5);

% 绘制从 Z_best 到 Z_new 的箭头
quiver(Z_best(1), Z_best(2), Z_new_gaussian(1), Z_new_gaussian(2), 0, 'Color', 'k', 'LineWidth', 1.5, 'MaxHeadSize', 0.5);

% 设置图表样式
title('高斯变异 (局部精细搜索)', 'FontName', 'SimHei', 'FontSize', 16);
xlabel('$Z_1$', 'Interpreter', 'latex', 'FontSize', 14);
ylabel('$Z_2$', 'Interpreter', 'latex', 'FontSize', 14, 'Rotation', 0);
xlim([-5 5]);
ylim([-5 5]);

% --- 4. 子图二：柯西变异 (全局跳跃搜索) ---
subplot(1, 2, 2);
hold on;
grid on;
axis equal;

% 生成柯西分布的随机点
cauchy_points_x = tan(pi * (rand(num_points, 1) - 0.5));
cauchy_points_y = tan(pi * (rand(num_points, 1) - 0.5));
cauchy_points = [cauchy_points_x, cauchy_points_y];

% 绘制由柯西变异产生的潜在新位置（点云）
plot(cauchy_points(:,1), cauchy_points(:,2), '.', 'Color', [0.3, 0.75, 0.93], 'MarkerSize', 8);

% 绘制当前最优解 Z_best
plot_Z_best_cauchy = plot(Z_best(1), Z_best(2), '*', 'Color', [0.9, 0.2, 0.2], 'MarkerSize', 16, 'LineWidth', 2);

% 选择一个点作为变异后的新位置 Z_new
[~, idx] = max(sqrt(sum(cauchy_points.^2, 2)));
Z_new_cauchy = cauchy_points(idx,:);
if abs(Z_new_cauchy(1)) > 4.5 || abs(Z_new_cauchy(2)) > 4.5
    Z_new_cauchy = [-2.8, 3.5];
end
plot_Z_new_cauchy = plot(Z_new_cauchy(1), Z_new_cauchy(2), '^', 'Color', [0.2, 0.7, 0.3], 'MarkerFaceColor', [0.2, 0.7, 0.3], 'MarkerSize', 10, 'LineWidth', 1.5);

% 绘制从 Z_best 到 Z_new 的箭头
quiver(Z_best(1), Z_best(2), Z_new_cauchy(1), Z_new_cauchy(2), 0, 'Color', 'k', 'LineWidth', 1.5, 'MaxHeadSize', 0.2);

% 设置图表样式
title('柯西变异 (全局跳跃搜索)', 'FontName', 'SimHei', 'FontSize', 16);
xlabel('$Z_1$', 'Interpreter', 'latex', 'FontSize', 14);
xlim([-5 5]);
ylim([-5 5]);
