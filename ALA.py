import numpy as np
import math
def ALA_optimization(fes_act_dec, fitness_func, predicted_steps):
    # 算法参数设置
    N = min(15, fes_act_dec.shape[0])
    Max_iter = 15
    vec_flag = np.array([1, -1])
    hist_best = [np.zeros((Max_iter, 2*predicted_steps)), np.zeros(Max_iter)]
    X = np.zeros((N, 2*predicted_steps))
    Xnew = np.zeros((N, 2*predicted_steps))
    Xnew_discrete = np.zeros((N, 2*predicted_steps))
    fitness = np.zeros(N)
    RB = np.zeros((N, 2*predicted_steps))
    
    # 初始化种群
    for i in range(N):
        indices = np.random.randint(0, fes_act_dec.shape[0], predicted_steps)
        X[i, :] = fes_act_dec[indices].flatten()
    
    # 适应度计算
    for i in range(N):
        fitness[i] = fitness_func(X[i, :])
    
    # 找到初始最优解
    best_idx = np.argmax(fitness)
    Position = X[best_idx:best_idx+1, :].copy()
    Score = fitness[best_idx]
    Iter = 1
    # 算法主循环
    while Iter <= Max_iter:
        RB = np.random.randn(N, 2*predicted_steps)  # 布朗运动
        F = vec_flag[np.random.randint(2)]  # 随机方向标志
        theta = 2*np.atan(1 - Iter/Max_iter)  # 时变参数
        p_cauchy = 1 - (Iter / Max_iter) # 柯西变异概率
        p_guassian = Iter / Max_iter # 高斯变异概率
        # 位置更新阶段
        for i in range(N):
            E = 2*np.log(1/np.random.rand())*theta
            if E > 1: # 高能量状态
                r = np.random.rand()
                # 柯西变异
                if r < p_cauchy and r > 0.3:
                    cauchy_step = 1.0 - (Iter / Max_iter)
                    cauchy_mutate = cauchy_step * np.tan(np.pi * (np.random.rand(1, 2*predicted_steps) - 0.5))
                    Xnew[i, :] = Position[0, :] + cauchy_mutate
                elif r <= 0.3:
                    # 群体协作
                    r1 = 2*np.random.rand(1, 2*predicted_steps) - 1
                    Xnew[i,:] = Position[0, :] + F*RB[i,:]*(r1*(Position-X[i,:])+(1-r1)*(X[i,:]-X[np.random.randint(N),:]))
                else: 
                    # 定向搜索
                    r2 = np.random.rand() * (1 + np.sin(0.5 * Iter))
                    Xnew[i,:] = X[i,:] + F* r2*(Position[0, :]-X[np.random.randint(N),:])
            else:
                r = np.random.rand()
                if r < p_guassian and r > 0.5:
                    guassian_step = 1 - (Iter/Max_iter)
                    guassian_mutate = guassian_step*np.random.randn(1,2*predicted_steps)
                    Xnew[i, :] = Position[0, :] + guassian_mutate
                elif r <= 0.5:
                    # 螺旋搜索
                    radius = np.sqrt(np.sum((Position[0, :]-X[i, :])**2))
                    r3 = np.random.rand()
                    spiral = radius*(np.sin(2*np.pi*r3)+np.cos(2*np.pi*r3))
                    Xnew[i,:] = Position[0, :] + F* X[i,:]*spiral*np.random.rand()
                else:
                    # 莱维飞行
                    G = 2*(np.sign(np.random.rand() - 0.5))*(1-Iter/Max_iter)
                    beta = 1.5
                    sigma = (math.gamma(1+beta)*np.sin(np.pi*beta/2)/(math.gamma((1+beta)/2)*beta*2**((beta-1)/2)))**(1/beta)
                    u = np.random.randn(1, 2*predicted_steps)*sigma
                    v = np.random.randn(1, 2*predicted_steps)
                    step = u/np.abs(v)**(1/beta)
                    Xnew[i,:] = Position[0, :] + F* G*step*(Position[0, :] - X[i,:])        
        # 连续解的离散化
        Xnew_discrete = discretize_solutions(Xnew, fes_act_dec, predicted_steps)
        
        # 初始化新种群适应度
        newPopfit = np.zeros(N)
        
        # 评估新解
        for i in range(N):
            newPopfit[i] = fitness_func(Xnew_discrete[i, :])
        
        # 贪心选择
        for i in range(N):
            if newPopfit[i] > fitness[i]:
                X[i, :] = Xnew_discrete[i, :]
                fitness[i] = newPopfit[i]
            # 更新全局最优
            if fitness[i] > Score:
                Position[0, :] = X[i, :]
                Score = fitness[i]
        hist_best[0][Iter-1, :] = Position[0,:]
        hist_best[1][Iter-1] = Score
        Iter += 1
    best_position = Position[0, :]
    return best_position, hist_best

# 离散化函数
def discretize_solutions(Xnew, fes_act_dec, predicted_steps):
    N = Xnew.shape[0]
    Xnew_discrete = np.zeros_like(Xnew)
    
    for i in range(N):
        for step in range(predicted_steps):
            start_idx = 2 * step
            end_idx = 2 * step + 2
            step_decision = Xnew[i, start_idx:end_idx]
            distances = np.sum((fes_act_dec - step_decision)**2, axis=1)
            min_idx = np.argmin(distances)
            Xnew_discrete[i, start_idx:end_idx] = fes_act_dec[min_idx, :]
    
    return Xnew_discrete

# 测试函数
def fitness_func(x):
    return -np.sum(x**2)

def generate_test_data():
    np.random.seed(42)
    # 生成整数的20x2可行域，范围在[-10, 10]
    fes_act_dec = np.random.randint(-10, 11, (20, 2)).astype(float)
    return fes_act_dec

# 测试代码
if __name__ == "__main__":
    fes_act_dec = generate_test_data()
    predicted_steps = 3
    best_pos, history = ALA_optimization(fes_act_dec, fitness_func, predicted_steps)
    print(f"最优解: {best_pos}")
    print(f"最优适应度: {history[1][-1]}")

